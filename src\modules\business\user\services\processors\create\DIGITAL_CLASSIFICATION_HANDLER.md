# Digital Classification Handler

## Tổng quan

`DigitalClassificationHandler` là một handler chuy<PERSON><PERSON> biệt để xử lý việc tạo classifications cho sản phẩm số (DIGITAL). Handler này được tách biệt từ `DigitalProductProcessor` để tối ưu hóa code organization và dễ dàng maintain.

## Chức năng chính

### 1. Xử lý Classifications
- Validate tất cả classifications trước khi tạo
- Tạo từng classification thông qua `ClassificationService`
- Xử lý hình ảnh và tạo upload URLs
- Tr<PERSON> về kết quả hoàn chỉnh với classifications và upload URLs

### 2. Validation
- **Duplicate Names**: Kiểm tra tên classification không trùng lặp
- **Duplicate SKUs**: Kiểm tra SKU không trùng lặp
- **Quantity Constraints**: Validate min/max quantity per purchase
- **Price Validation**: Kiểm tra gi<PERSON> hợp lệ (sale price ≤ list price)
- **Field Length**: Kiểm tra độ dài các trường
- **Required Fields**: Kiểm tra các trường bắt buộc

### 3. Image Handling
- Tạo S3 keys cho hình ảnh classifications
- Generate presigned URLs cho upload
- Hỗ trợ multiple images per classification

## Cách sử dụng

### 1. Trong DigitalProductProcessor

```typescript
@Injectable()
export class DigitalProductProcessor {
  constructor(
    // ... other dependencies
    private readonly digitalClassificationHandler: DigitalClassificationHandler,
  ) {}

  async createDigitalProduct(dto: DigitalProductCreateDto, userId: number) {
    // ... other steps

    // Xử lý classifications
    const classificationResult = await this.digitalClassificationHandler.processClassifications(
      savedProduct.id, 
      dto.classifications || [], 
      userId
    );
    
    const classifications = classificationResult.classifications;
    const classificationUploadUrls = classificationResult.uploadUrls;

    // ... continue processing
  }
}
```

### 2. Input/Output

**Input:**
```typescript
interface ProcessClassificationsInput {
  productId: number;
  classificationsDto: DigitalClassificationDto[];
  userId: number;
}
```

**Output:**
```typescript
interface ProcessClassificationsOutput {
  classifications: ClassificationResponseDto[];
  uploadUrls: Array<{
    url: string;
    key: string;
    index: number;
    classificationId: number;
    classificationName: string;
    mediaType: string;
  }>;
}
```

## Validation Rules

### 1. Classification Level
- `name`: Required, max 255 characters, unique
- `sku`: Required, max 255 characters, unique
- `description`: Optional, max 1000 characters
- `minQuantityPerPurchase`: Required, > 0
- `maxQuantityPerPurchase`: Required, > 0, >= minQuantityPerPurchase
- `availableQuantity`: Required, >= 0
- `price`: Required object with valid listPrice and salePrice
- `imagesMediaTypes`: Optional, max 10 images

### 2. Price Validation
- `listPrice`: Required, > 0
- `salePrice`: Required, > 0, <= listPrice
- `currency`: Required string

### 3. Image Validation
- Maximum 10 images per classification
- Valid MIME types for images
- Generates S3 keys with timestamp

## Error Handling

Handler sẽ throw `AppException` với `BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED` trong các trường hợp:

1. **Duplicate Names**: `Duplicate classification name: {name}`
2. **Duplicate SKUs**: `Duplicate SKU: {sku}`
3. **Invalid Name**: `Classification name is required`
4. **Invalid SKU**: `SKU is required for classification: {name}`
5. **Invalid Quantities**: `Minimum quantity cannot be greater than maximum quantity`
6. **Invalid Prices**: `Sale price cannot be greater than list price`
7. **Field Too Long**: `Classification name too long: {name}`
8. **Too Many Images**: `Too many images for classification: {name}. Maximum 10 images allowed.`

## Dependencies

### 1. ClassificationService
- Sử dụng để tạo classification entities
- Handle business logic cho classification creation

### 2. S3Service
- Generate presigned URLs cho image upload
- Handle S3 key generation

## Testing

Handler được test với:
- Unit tests cho tất cả validation rules
- Integration tests với mock dependencies
- Error handling tests
- Image upload URL generation tests

### Chạy tests:
```bash
npm run test -- digital-classification.handler.spec.ts
```

## Performance Considerations

1. **Batch Validation**: Validate tất cả classifications trước khi tạo
2. **Early Return**: Return empty arrays nếu không có classifications
3. **Efficient Loops**: Sử dụng for...of thay vì forEach cho async operations
4. **Memory Management**: Không store large objects unnecessarily

## Future Enhancements

1. **Batch Creation**: Có thể optimize để tạo multiple classifications trong một transaction
2. **Caching**: Cache validation results cho performance
3. **Async Image Processing**: Process images asynchronously
4. **Rollback Mechanism**: Implement rollback nếu có lỗi trong quá trình tạo

## Module Registration

Handler được đăng ký trong `BusinessUserModule`:

```typescript
@Module({
  providers: [
    // ... other providers
    DigitalClassificationHandler,
  ],
})
export class BusinessUserModule {}
```

## Logs

Handler tạo logs cho:
- Số lượng classifications được process
- Validation success/failure
- Classification creation success
- Upload URL generation
- Error details

Log level: `INFO` cho normal operations, `ERROR` cho exceptions.
