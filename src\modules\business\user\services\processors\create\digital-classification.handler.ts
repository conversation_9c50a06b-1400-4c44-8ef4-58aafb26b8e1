import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ClassificationService } from '../../classification.service';
import { DigitalClassificationDto } from '../../../dto/digital-classification.dto';
import { CreateClassificationDto, ClassificationResponseDto } from '../../../dto/classification.dto';
import { S3Service } from '@shared/services/s3.service';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';

/**
 * Handler chuyên xử lý logic tạo classifications cho sản phẩm số
 * Tách biệt từ processor chính để dễ maintain và test
 */
@Injectable()
export class DigitalClassificationHandler {
  private readonly logger = new Logger(DigitalClassificationHandler.name);

  constructor(
    private readonly classificationService: ClassificationService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Xử lý tạo tất cả classifications cho sản phẩm số
   */
  async processClassifications(
    productId: number,
    classificationsDto: DigitalClassificationDto[],
    userId: number,
  ): Promise<{
    classifications: ClassificationResponseDto[];
    uploadUrls: any[];
  }> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return { classifications: [], uploadUrls: [] };
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for digital product: ${productId}`);

    // Validate tất cả classifications trước khi tạo
    await this.validateAllClassifications(classificationsDto);

    const createdClassifications: ClassificationResponseDto[] = [];
    const allUploadUrls: any[] = [];

    // Tạo từng classification
    for (const digitalClassificationDto of classificationsDto) {
      try {
        const result = await this.createSingleClassification(
          productId,
          digitalClassificationDto,
          userId,
        );

        createdClassifications.push(result.classification);
        if (result.uploadUrls.length > 0) {
          allUploadUrls.push(...result.uploadUrls);
        }

        this.logger.log(`Created classification: ${result.classification.id} (${digitalClassificationDto.name}) for product: ${productId}`);
      } catch (error) {
        this.logger.error(`Failed to create classification "${digitalClassificationDto.name}" for product ${productId}: ${error.message}`, error.stack);
        throw error;
      }
    }

    this.logger.log(`Successfully created ${createdClassifications.length} classifications for digital product: ${productId}`);
    return {
      classifications: createdClassifications,
      uploadUrls: allUploadUrls,
    };
  }

  /**
   * Validate tất cả classifications
   */
  private async validateAllClassifications(classificationsDto: DigitalClassificationDto[]): Promise<void> {
    const classificationNames = new Set<string>();
    const skus = new Set<string>();

    for (const classification of classificationsDto) {
      // Validate duplicate names
      if (classificationNames.has(classification.name)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Duplicate classification name: ${classification.name}`,
        );
      }
      classificationNames.add(classification.name);

      // Validate duplicate SKUs
      if (skus.has(classification.sku)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Duplicate SKU: ${classification.sku}`,
        );
      }
      skus.add(classification.sku);

      // Validate individual classification
      await this.validateSingleClassification(classification);
    }

    this.logger.log(`Validated ${classificationsDto.length} classifications successfully`);
  }

  /**
   * Validate một classification
   */
  private async validateSingleClassification(classification: DigitalClassificationDto): Promise<void> {
    // Validate name
    if (!classification.name || classification.name.trim().length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Classification name is required',
      );
    }

    if (classification.name.length > 255) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Classification name too long: ${classification.name}`,
      );
    }

    // Validate SKU
    if (!classification.sku || classification.sku.trim().length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `SKU is required for classification: ${classification.name}`,
      );
    }

    if (classification.sku.length > 255) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `SKU too long for classification: ${classification.name}`,
      );
    }

    // Validate quantity constraints
    if (classification.minQuantityPerPurchase <= 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Minimum quantity must be greater than 0 for classification: ${classification.name}`,
      );
    }

    if (classification.maxQuantityPerPurchase <= 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Maximum quantity must be greater than 0 for classification: ${classification.name}`,
      );
    }

    if (classification.minQuantityPerPurchase > classification.maxQuantityPerPurchase) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Minimum quantity cannot be greater than maximum quantity for classification: ${classification.name}`,
      );
    }

    // Validate available quantity
    if (classification.availableQuantity < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Available quantity cannot be negative for classification: ${classification.name}`,
      );
    }

    // Validate price
    if (!classification.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Price is required for classification: ${classification.name}`,
      );
    }

    // Validate price values
    if (classification.price.listPrice <= 0 || classification.price.salePrice <= 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Price values must be greater than 0 for classification: ${classification.name}`,
      );
    }

    if (classification.price.salePrice > classification.price.listPrice) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Sale price cannot be greater than list price for classification: ${classification.name}`,
      );
    }

    // Validate description
    if (classification.description && classification.description.length > 1000) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Description too long for classification: ${classification.name}`,
      );
    }

    // Validate images media types
    if (classification.imagesMediaTypes && classification.imagesMediaTypes.length > 10) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Too many images for classification: ${classification.name}. Maximum 10 images allowed.`,
      );
    }

    this.logger.log(`Validated classification: ${classification.name}`);
  }

  /**
   * Tạo một classification
   */
  private async createSingleClassification(
    productId: number,
    digitalClassificationDto: DigitalClassificationDto,
    userId: number,
  ): Promise<{
    classification: ClassificationResponseDto;
    uploadUrls: any[];
  }> {
    // Mapping từ DigitalClassificationDto sang CreateClassificationDto
    const createClassificationDto = this.mapDigitalToCreateClassificationDto(digitalClassificationDto);

    // Tạo classification thông qua ClassificationService
    const createdClassification = await this.classificationService.create(
      productId,
      createClassificationDto,
      userId,
    );

    // Tạo upload URLs cho hình ảnh nếu có
    const uploadUrls = await this.generateImageUploadUrls(
      digitalClassificationDto,
      createdClassification.id,
    );

    return {
      classification: createdClassification,
      uploadUrls,
    };
  }

  /**
   * Tạo upload URLs cho hình ảnh classification
   */
  private async generateImageUploadUrls(
    classification: DigitalClassificationDto,
    classificationId: number,
  ): Promise<any[]> {
    if (!classification.imagesMediaTypes || classification.imagesMediaTypes.length === 0) {
      return [];
    }

    const uploadUrls: any[] = [];
    const timestamp = Date.now();

    for (let i = 0; i < classification.imagesMediaTypes.length; i++) {
      const mediaType = classification.imagesMediaTypes[i];
      
      // Generate S3 key
      const key = generateS3Key({
        category: CategoryFolderEnum.BUSINESS,
        subCategory: 'IMAGE',
        fileName: `classification-${classificationId}-image-${i}-${timestamp}`,
      });

      // Generate presigned URL
      const presignedUrl = await this.s3Service.generatePresignedUrl(
        key,
        mediaType,
        FileSizeEnum.IMAGE_MAX_SIZE,
        TimeIntervalEnum.UPLOAD_EXPIRY,
      );

      uploadUrls.push({
        url: presignedUrl,
        key,
        index: i,
        classificationId,
        classificationName: classification.name,
        mediaType,
      });
    }

    this.logger.log(`Generated ${uploadUrls.length} upload URLs for classification: ${classification.name}`);
    return uploadUrls;
  }

  /**
   * Mapping từ DigitalClassificationDto sang CreateClassificationDto
   */
  private mapDigitalToCreateClassificationDto(digitalDto: DigitalClassificationDto): CreateClassificationDto {
    return {
      type: digitalDto.name, // Sử dụng name làm type
      description: digitalDto.description,
      price: digitalDto.price,
      customFields: digitalDto.customFields || [],
      imagesMediaTypes: digitalDto.imagesMediaTypes || [],
      sku: digitalDto.sku,
      availableQuantity: digitalDto.availableQuantity,
      minQuantityPerPurchase: digitalDto.minQuantityPerPurchase,
      maxQuantityPerPurchase: digitalDto.maxQuantityPerPurchase,
    };
  }
}
