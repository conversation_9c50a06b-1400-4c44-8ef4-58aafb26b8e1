import { Test, TestingModule } from '@nestjs/testing';
import { DigitalClassificationHandler } from './digital-classification.handler';
import { ClassificationService } from '../../classification.service';
import { S3Service } from '@shared/services/s3.service';
import { DigitalClassificationDto } from '../../../dto/digital-classification.dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

describe('DigitalClassificationHandler', () => {
  let handler: DigitalClassificationHandler;
  let classificationService: jest.Mocked<ClassificationService>;
  let s3Service: jest.Mocked<S3Service>;

  beforeEach(async () => {
    const mockClassificationService = {
      create: jest.fn(),
    };

    const mockS3Service = {
      generatePresignedUrl: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DigitalClassificationHandler,
        {
          provide: ClassificationService,
          useValue: mockClassificationService,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
      ],
    }).compile();

    handler = module.get<DigitalClassificationHandler>(DigitalClassificationHandler);
    classificationService = module.get(ClassificationService);
    s3Service = module.get(S3Service);
  });

  describe('processClassifications', () => {
    it('should return empty arrays when no classifications provided', async () => {
      const result = await handler.processClassifications(1, [], 1);
      
      expect(result).toEqual({
        classifications: [],
        uploadUrls: [],
      });
    });

    it('should process valid classifications successfully', async () => {
      const mockClassifications: DigitalClassificationDto[] = [
        {
          name: 'Basic',
          sku: 'BASIC-001',
          availableQuantity: 10,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 5,
          price: {
            listPrice: 100000,
            salePrice: 80000,
            currency: 'VND',
          },
          description: 'Basic package',
          imagesMediaTypes: ['image/jpeg'],
          customFields: [],
        },
      ];

      const mockCreatedClassification = {
        id: 1,
        type: 'Basic',
        description: 'Basic package',
        price: mockClassifications[0].price,
        customFields: [],
        sku: 'BASIC-001',
        minQuantityPerPurchase: 1,
        maxQuantityPerPurchase: 5,
        imagesMediaTypes: [],
      };

      classificationService.create.mockResolvedValue(mockCreatedClassification);
      s3Service.generatePresignedUrl.mockResolvedValue('https://presigned-url.com');

      const result = await handler.processClassifications(1, mockClassifications, 1);

      expect(result.classifications).toHaveLength(1);
      expect(result.classifications[0]).toEqual(mockCreatedClassification);
      expect(result.uploadUrls).toHaveLength(1);
      expect(classificationService.create).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          type: 'Basic',
          description: 'Basic package',
          price: mockClassifications[0].price,
          sku: 'BASIC-001',
        }),
        1,
      );
    });

    it('should throw error for duplicate classification names', async () => {
      const mockClassifications: DigitalClassificationDto[] = [
        {
          name: 'Basic',
          sku: 'BASIC-001',
          availableQuantity: 10,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 5,
          price: { listPrice: 100000, salePrice: 80000, currency: 'VND' },
          description: 'Basic package',
          imagesMediaTypes: [],
          customFields: [],
        },
        {
          name: 'Basic', // Duplicate name
          sku: 'BASIC-002',
          availableQuantity: 10,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 5,
          price: { listPrice: 100000, salePrice: 80000, currency: 'VND' },
          description: 'Basic package 2',
          imagesMediaTypes: [],
          customFields: [],
        },
      ];

      await expect(
        handler.processClassifications(1, mockClassifications, 1)
      ).rejects.toThrow(AppException);
    });

    it('should throw error for duplicate SKUs', async () => {
      const mockClassifications: DigitalClassificationDto[] = [
        {
          name: 'Basic',
          sku: 'BASIC-001',
          availableQuantity: 10,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 5,
          price: { listPrice: 100000, salePrice: 80000, currency: 'VND' },
          description: 'Basic package',
          imagesMediaTypes: [],
          customFields: [],
        },
        {
          name: 'Premium',
          sku: 'BASIC-001', // Duplicate SKU
          availableQuantity: 10,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 5,
          price: { listPrice: 100000, salePrice: 80000, currency: 'VND' },
          description: 'Premium package',
          imagesMediaTypes: [],
          customFields: [],
        },
      ];

      await expect(
        handler.processClassifications(1, mockClassifications, 1)
      ).rejects.toThrow(AppException);
    });

    it('should throw error for invalid quantity constraints', async () => {
      const mockClassifications: DigitalClassificationDto[] = [
        {
          name: 'Basic',
          sku: 'BASIC-001',
          availableQuantity: 10,
          minQuantityPerPurchase: 5,
          maxQuantityPerPurchase: 3, // Min > Max
          price: { listPrice: 100000, salePrice: 80000, currency: 'VND' },
          description: 'Basic package',
          imagesMediaTypes: [],
          customFields: [],
        },
      ];

      await expect(
        handler.processClassifications(1, mockClassifications, 1)
      ).rejects.toThrow(AppException);
    });

    it('should throw error for invalid price values', async () => {
      const mockClassifications: DigitalClassificationDto[] = [
        {
          name: 'Basic',
          sku: 'BASIC-001',
          availableQuantity: 10,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 5,
          price: { 
            listPrice: 80000, 
            salePrice: 100000, // Sale price > List price
            currency: 'VND' 
          },
          description: 'Basic package',
          imagesMediaTypes: [],
          customFields: [],
        },
      ];

      await expect(
        handler.processClassifications(1, mockClassifications, 1)
      ).rejects.toThrow(AppException);
    });

    it('should generate upload URLs for images', async () => {
      const mockClassifications: DigitalClassificationDto[] = [
        {
          name: 'Basic',
          sku: 'BASIC-001',
          availableQuantity: 10,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 5,
          price: { listPrice: 100000, salePrice: 80000, currency: 'VND' },
          description: 'Basic package',
          imagesMediaTypes: ['image/jpeg', 'image/png'],
          customFields: [],
        },
      ];

      const mockCreatedClassification = {
        id: 1,
        type: 'Basic',
        description: 'Basic package',
        price: mockClassifications[0].price,
        customFields: [],
        sku: 'BASIC-001',
        minQuantityPerPurchase: 1,
        maxQuantityPerPurchase: 5,
        imagesMediaTypes: [],
      };

      classificationService.create.mockResolvedValue(mockCreatedClassification);
      s3Service.generatePresignedUrl.mockResolvedValue('https://presigned-url.com');

      const result = await handler.processClassifications(1, mockClassifications, 1);

      expect(result.uploadUrls).toHaveLength(2); // 2 images
      expect(s3Service.generatePresignedUrl).toHaveBeenCalledTimes(2);
      expect(result.uploadUrls[0]).toMatchObject({
        url: 'https://presigned-url.com',
        index: 0,
        classificationId: 1,
        classificationName: 'Basic',
        mediaType: 'image/jpeg',
      });
    });
  });
});
