-- Migration: Add missing fields to user_classifications table
-- Date: 2024-12-19
-- Description: Thê<PERSON> các trường còn thiếu vào bảng user_classifications để khớp với UserClassification entity

-- Thêm cột duration (thời lượng dịch vụ - phút)
ALTER TABLE user_classifications
ADD COLUMN IF NOT EXISTS duration INTEGER NULL;

COMMENT ON COLUMN user_classifications.duration
IS 'Thời lượng dịch vụ (phút) - cho service packages';

-- Thêm cột start_time (thời gian bắt đầu - timestamp)
ALTER TABLE user_classifications
ADD COLUMN IF NOT EXISTS start_time BIGINT NULL;

COMMENT ON COLUMN user_classifications.start_time
IS 'Thời gian bắt đầu (timestamp) - cho service packages';

-- Thêm cột end_time (thời gian kết thúc - timestamp)
ALTER TABLE user_classifications
ADD COLUMN IF NOT EXISTS end_time BIGINT NULL;

COMMENT ON COLUMN user_classifications.end_time
IS 'Thời gian kết thúc (timestamp) - cho service packages';

-- Thêm cột timezone (múi giờ)
ALTER TABLE user_classifications
ADD COLUMN IF NOT EXISTS timezone VARCHAR(100) NULL;

COMMENT ON COLUMN user_classifications.timezone
IS 'Múi giờ - cho service packages';

-- Thêm cột status (trạng thái)
ALTER TABLE user_classifications
ADD COLUMN IF NOT EXISTS status VARCHAR(50) NULL;

COMMENT ON COLUMN user_classifications.status
IS 'Trạng thái - cho service packages';

-- Thêm cột quantity (số lượng có sẵn)
ALTER TABLE user_classifications
ADD COLUMN IF NOT EXISTS quantity INTEGER NULL;

COMMENT ON COLUMN user_classifications.quantity
IS 'Số lượng có sẵn - cho service packages';

-- Thêm cột custom_fields (trường tùy chỉnh)
ALTER TABLE user_classifications
ADD COLUMN IF NOT EXISTS custom_fields JSONB NULL;

COMMENT ON COLUMN user_classifications.custom_fields
IS 'Trường tùy chỉnh (jsonb)';

-- Thêm cột images_media (thông tin media hình ảnh)
ALTER TABLE user_classifications
ADD COLUMN IF NOT EXISTS images_media JSONB NULL;

COMMENT ON COLUMN user_classifications.images_media
IS 'Thông tin media hình ảnh (jsonb)';

-- Kiểm tra kết quả migration
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns
WHERE table_name = 'user_classifications'
ORDER BY ordinal_position;
